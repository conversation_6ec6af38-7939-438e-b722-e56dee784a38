import cn.mlamp.insightflow.cms.model.dto.DifyScriptGenerationRequestDTO;
import cn.mlamp.insightflow.cms.model.query.ScriptGenerationRequest;
import cn.mlamp.insightflow.cms.util.dify.DifyUtil;
import cn.mlamp.insightflow.cms.util.dify.model.DifyRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.Map;
import java.util.UUID;

/**
 * 脚本生成测试
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
public class ScriptGenerationTest {

    @Test
    public void testScriptGeneration() {
        try {
            // 创建测试请求
            ScriptGenerationRequest request = new ScriptGenerationRequest();
            request.setBrand("小米");
            request.setProduct("小米手机");
            request.setSellingPoint("高性价比");
            request.setUserStory("年轻人追求性价比的故事");
            request.setPurchaseWording("限时优惠，错过再等一年");
            request.setPicture("产品展示+使用场景");
            request.setWording("开场引入+产品介绍+逼单");

            // 构建Dify请求参数
            Map<String, Object> params = DifyScriptGenerationRequestDTO.buildDifyParams(request);
            log.info("Dify请求参数: {}", params);

            // 构建Dify请求
            var difyRequest = DifyRequest.builder()
                    .appKey("app-cZyLBQVtTkxe2XCJSg40OjMD")
                    .baseUrl("https://llm-ops-social.mlamp.cn/v1")
                    .inputsParams(params)
                    .observationId(UUID.randomUUID().toString().replace("-", ""))
                    .build();

            // 调用Dify API（注释掉实际调用，避免测试时真实调用）
            /*
            DifyUtil difyUtil = new DifyUtil();
            var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);
            log.info("Dify返回结果: {}", difyResult);
            */

            log.info("脚本生成测试完成");

        } catch (Exception e) {
            log.error("脚本生成测试失败", e);
        }
    }
}
