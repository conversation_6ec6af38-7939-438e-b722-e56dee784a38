import cn.mlamp.insightflow.cms.model.dto.DifyScriptGenerationRequestDTO;
import cn.mlamp.insightflow.cms.model.query.ScriptGenerationRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.Map;

/**
 * 脚本生成测试
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
public class ScriptGenerationTest {

    @Test
    public void testScriptGeneration() {
        try {
            // 创建测试请求
            ScriptGenerationRequest request = new ScriptGenerationRequest();
            request.setBrand("小米");
            request.setProduct("小米手机");
            request.setSellingPoint("高性价比");
            request.setUserStory("年轻人追求性价比的故事");
            request.setPurchaseWording("限时优惠，错过再等一年");
            request.setPicture("产品展示+使用场景");
            request.setWording("开场引入+产品介绍+逼单");

            // 构建Dify请求参数
            Map<String, Object> params = DifyScriptGenerationRequestDTO.buildDifyParams(request);
            log.info("Dify请求参数: {}", params);

            // 验证参数是否正确构建
            assert params.containsKey("brand");
            assert params.containsKey("product");
            assert params.containsKey("sellingPoint");
            assert params.containsKey("userStory");
            assert params.containsKey("purchaseWording");
            assert params.containsKey("picture");
            assert params.containsKey("wording");

            assert params.get("brand").equals("小米");
            assert params.get("product").equals("小米手机");

            log.info("脚本生成测试完成");

        } catch (Exception e) {
            log.error("脚本生成测试失败", e);
        }
    }
}
