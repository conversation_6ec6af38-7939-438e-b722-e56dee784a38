package cn.mlamp.insightflow.cms.service.impl;

import cn.mlamp.insightflow.cms.entity.CmsVideoFiveGold;
import cn.mlamp.insightflow.cms.entity.CmsVideoInfo;
import cn.mlamp.insightflow.cms.entity.CmsVideoThreeGoldRelation;
import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import cn.mlamp.insightflow.cms.mapper.QianchuanMaterialVideoMapper;
import cn.mlamp.insightflow.cms.mapper.VideoFiveGoldMapper;
import cn.mlamp.insightflow.cms.mapper.VideoThreeGoldRelationMapper;
import cn.mlamp.insightflow.cms.config.AnalysisVideoConfig;
import cn.mlamp.insightflow.cms.service.IGoldFiveSecondTaskService;
import cn.mlamp.insightflow.cms.service.IVideoInfoService;
import cn.mlamp.insightflow.cms.service.IVideoResultService;
import cn.mlamp.insightflow.cms.util.DateUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 黄金五秒任务服务实现类
 *
 * <AUTHOR> yangzhibo
 * @date : 2025-5-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoldFiveSecondTaskServiceImpl extends ServiceImpl<VideoFiveGoldMapper, CmsVideoFiveGold>
        implements IGoldFiveSecondTaskService {

    @Autowired
    private IVideoInfoService videoInfoService;

    @Autowired
    private IVideoResultService videoResultService;

    @Autowired
    private AnalysisVideoConfig analysisVideoConfig;

    @Autowired
    private QianchuanMaterialVideoMapper qianchuanMaterialVideoMapper;

    @Autowired
    private VideoThreeGoldRelationMapper videoThreeGoldRelationMapper;

    // 行业列表常量
    private static final List<String> INDUSTRY_LIST = Arrays.asList("护肤品", "内衣家居服", "个人护理", "女装", "饮料乳品", "彩妆香水",
            "零食特产", "家庭清洁", "保健品", "滋补品", "男鞋", "男装", "女鞋", "方便速食", "母婴食品", "生活日用", "茶类", "时尚饰品", "洗发护发", "美妆工具",
            "3C/手机");

    // 黄金5秒视频标签常量
    private static final List<String> GOLD_FIVE_SECOND_TAGS = Arrays.asList("产生情感共鸣", "制造悬念", "引发食欲", "引发思考", "意外转场",
            "引发期待", "制造焦虑和痛点", "擦边", "制造反差", "产品对比", "制造高级感", "解压", "承诺吸引", "搭建人设", "福利性价比");

    public static final String GOLD_FIVE_SECOND_KEY = "goldFiveSecondJob.analysis.";

    // 查询视频数量限制
    @org.springframework.beans.factory.annotation.Value("${gold.five.second.video.limit:1000}")
    private int videoQueryLimit;

    // 组大小阈值
    @org.springframework.beans.factory.annotation.Value("${gold.five.second.group.size.threshold:2}")
    private int groupSizeThreshold;

    @Override
    public void processGoldFiveSecondTask(String dateStr) {
        log.info("开始处理黄金五秒任务，日期：{}", dateStr);

        // 遍历行业列表
        for (String industry : INDUSTRY_LIST) {
            // 遍历黄金5秒视频标签
            for (String tag : GOLD_FIVE_SECOND_TAGS) {
                try {
                    // 执行黄金五秒计算任务
                    executeGoldFiveSecondCalculation(dateStr, industry, tag);
                } catch (Exception e) {
                    log.error("处理黄金五秒任务失败，日期：{}，行业：{}，标签：{}", dateStr, industry, tag, e);
                }
            }
        }
    }

    /**
     * 执行黄金五秒计算任务 查询视频数量限制由配置参数 gold.five.second.video.limit 指定，默认为 1000
     *
     * @param dateStr  日期字符串
     * @param industry 行业
     * @param tag      标签
     */
    private void executeGoldFiveSecondCalculation(String dateStr, String industry, String tag) {
        log.info("执行黄金五秒计算任务，日期：{}，行业：{}，标签：{}", dateStr, industry, tag);

        try {
            // 1. 查询符合条件的视频数据
            // 从 cms_video_info 表中查询符合条件的视频数据
            // 条件：type = 5，status = 3，three_gold_type = 标签，industry = 行业
            // 按 update_time 降序排序，取前 videoQueryLimit 条
            List<CmsVideoInfo> videoList = videoInfoService.findVideosByCondition(5, 3, tag, industry, videoQueryLimit);
            log.info("查询到 {} 条符合条件的视频数据，日期：{}，行业：{}，标签：{}", videoList.size(), dateStr, industry, tag);

            if (videoList.isEmpty()) {
                log.info("没有查询到符合条件的视频数据，跳过处理，日期：{}，行业：{}，标签：{}", dateStr, industry, tag);
                return;
            }

            // 提取视频ID列表
            List<Integer> videoIds = videoList.stream().map(CmsVideoInfo::getId).collect(Collectors.toList());
            List<String> videoEeIds = videoList.stream().map(CmsVideoInfo::getEsId).collect(Collectors.toList());

            // 查询视频的ASR 5秒结果
            Map<String, String> esIdToAsr5Map = videoResultService.getVideoAsr5Results(videoIds);
            log.info("查询到 {} 条视频的ASR 5秒结果，日期：{}，行业：{}，标签：{}", esIdToAsr5Map.size(), dateStr, industry, tag);

            if (esIdToAsr5Map.isEmpty()) {
                log.info("没有查询到视频的ASR 5秒结果，跳过处理，日期：{}，行业：{}，标签：{}", dateStr, industry, tag);
                return;
            }

            // 2. 调用聚类接口，对ASR 5秒结果进行聚类
            Map<String, List<String>> clusterGroups = clusterAsr5Results(esIdToAsr5Map);
            log.info("聚类得到 {} 个组，日期：{}，行业：{}，标签：{}", clusterGroups.size(), dateStr, industry, tag);

            // 3. 筛选出长度大于5的组，并构建组别到ASR 5秒值列表的映射
            Map<String, List<String>> filteredGroups = filterAndBuildGroupToAsr5ListMap(clusterGroups, esIdToAsr5Map);
            log.info("筛选后得到 {} 个组，日期：{}，行业：{}，标签：{}", filteredGroups.size(), dateStr, industry, tag);

            // 4. 使用多线程调用Dify工作流API生成脚本
            Map<String, String> groupScriptMap = generateScriptsInParallel(filteredGroups);

            // 5. 保存计算结果
            saveCalculationResults(dateStr, industry, tag, videoEeIds, clusterGroups, groupScriptMap, esIdToAsr5Map);

            log.info("黄金五秒计算任务执行完成，日期：{}，行业：{}，标签：{}", dateStr, industry, tag);
        } catch (Exception e) {
            log.error("执行黄金五秒计算任务失败，日期：{}，行业：{}，标签：{}", dateStr, industry, tag, e);
            throw new RuntimeException("执行黄金五秒计算任务失败", e);
        }
    }

    /**
     * 调用聚类接口，对ASR 5秒结果进行聚类
     *
     * @param esIdToAsr5Map 以视频esId为键，ASR 5秒结果为值的Map
     * @return 以组别为键，esId列表为值的Map
     * @throws JSONException JSON解析异常
     */
    private Map<String, List<String>> clusterAsr5Results(Map<String, String> esIdToAsr5Map) throws JSONException {
        // 构建请求JSON
        JSONObject dataJson = new JSONObject();
        for (Map.Entry<String, String> entry : esIdToAsr5Map.entrySet()) {
            dataJson.put(entry.getKey(), entry.getValue());
        }

        JSONObject requestJson = new JSONObject();
        requestJson.put("min_sim", 0.7); // 设置相似度阈值为0.7
        requestJson.put("k", 100);
        requestJson.put("mask_len", 512); // 设置最大长度为512
        requestJson.put("data", dataJson);

        // 发送HTTP请求
        HttpResponse response = null;
        String responseBody = null;
        try {
            response = HttpRequest.post("http://10.10.100.228:8103/cluster").header("Content-Type", "application/json")
                    .body(requestJson.toString()).timeout(180 * 1000).execute();
            responseBody = response.body();
        } catch (Exception e) {
            log.error("聚类请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("聚类请求失败，原因：" + e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }

        // 解析响应结果
        JSONObject responseJson = new JSONObject(responseBody);
        JSONObject resultJson = responseJson.optJSONObject("result");
        if (resultJson == null) {
            log.warn("聚类结果为空");
            return new HashMap<>();
        }

        // 构建分类映射: Map<类别, List<es_id>>
        Map<String, List<String>> clusterGroups = new HashMap<>();
        for (Iterator<String> it = resultJson.keys(); it.hasNext();) {
            String esId = it.next();
            String clusterId = String.valueOf(resultJson.get(esId)); // 获取分类 ID
            clusterGroups.computeIfAbsent(clusterId, k -> new ArrayList<>()).add(esId);
        }

        return clusterGroups;
    }

    /**
     * 筛选出长度大于阈值的组，并构建组别到ASR 5秒值列表的映射 阈值由配置参数 gold.five.second.group.size.threshold
     * 指定，默认为 2
     *
     * @param clusterGroups 以组别为键，esId列表为值的Map
     * @param esIdToAsr5Map 以视频esId为键，ASR 5秒结果为值的Map
     * @return 以组别为键，ASR 5秒值列表为值的Map
     */
    private Map<String, List<String>> filterAndBuildGroupToAsr5ListMap(Map<String, List<String>> clusterGroups,
            Map<String, String> esIdToAsr5Map) {
        Map<String, List<String>> groupToAsr5ListMap = new HashMap<>();

        for (Map.Entry<String, List<String>> entry : clusterGroups.entrySet()) {
            String groupId = entry.getKey();
            List<String> esIdList = entry.getValue();

            // 筛选出长度大于阈值的组
            if (esIdList.size() > groupSizeThreshold) {
                // 构建该组的ASR 5秒值列表
                List<String> asr5List = new ArrayList<>();
                for (String esId : esIdList) {
                    String asr5 = esIdToAsr5Map.get(esId);
                    if (asr5 != null) {
                        asr5List.add(asr5);
                    }
                }

                // 将组别和ASR 5秒值列表添加到结果中
                groupToAsr5ListMap.put(groupId, asr5List);
            }
        }

        return groupToAsr5ListMap;
    }

    /**
     * 使用多线程并行生成脚本
     *
     * @param filteredGroups 组别到ASR 5秒值列表的映射
     * @return 组别到脚本的映射
     */
    private Map<String, String> generateScriptsInParallel(Map<String, List<String>> filteredGroups) {
        log.info("开始使用多线程并行生成脚本，组数：{}", filteredGroups.size());

        // 创建结果映射
        Map<String, String> groupScriptMap = new ConcurrentHashMap<>();

        // 创建线程池
        int corePoolSize = Math.min(Runtime.getRuntime().availableProcessors(), 10); // 最多10个线程
        ExecutorService executor = Executors.newFixedThreadPool(corePoolSize);

        // 创建任务列表
        List<Future<?>> futures = new ArrayList<>();

        try {
            // 提交任务
            for (Map.Entry<String, List<String>> entry : filteredGroups.entrySet()) {
                String groupId = entry.getKey();
                List<String> asr5List = entry.getValue();

                // 创建任务
                Future<?> future = executor.submit(() -> {
                    try {
                        // 调用黄金3秒总结API生成脚本
                        String script = generateScript(asr5List);
                        groupScriptMap.put(groupId, script);
                        log.info("组ID：{} 的脚本生成成功", groupId);
                    } catch (Exception e) {
                        log.error("组ID：{} 的脚本生成失败", groupId, e);
                        groupScriptMap.put(groupId, "黄金3秒总结API生成台词失败");
                    }
                });

                futures.add(future);
            }

            // 等待所有任务完成
            for (Future<?> future : futures) {
                try {
                    future.get(5, TimeUnit.MINUTES); // 设置超时时间为5分钟
                } catch (TimeoutException e) {
                    log.error("脚本生成任务超时", e);
                    future.cancel(true);
                } catch (Exception e) {
                    log.error("等待脚本生成任务完成时发生异常", e);
                }
            }
        } finally {
            // 关闭线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        log.info("多线程并行生成脚本完成，成功生成脚本数：{}", groupScriptMap.size());
        return groupScriptMap;
    }

    /**
     * 调用新的黄金3秒总结API生成脚本，带重试机制
     *
     * @param asr5List ASR 5秒值列表
     * @return 生成的脚本
     * @throws Exception 异常信息
     */
    private String generateScript(List<String> asr5List) throws Exception {
        log.info("调用黄金3秒总结API生成脚本中...");

        // 使用配置文件中的分析服务地址
        String baseUrl = analysisVideoConfig.getAnalyzeUrl();

        // 最大重试次数
        int maxRetries = 3;
        // 当前重试次数
        int retryCount = 0;
        // 重试间隔（毫秒）
        int retryInterval = 1000;

        Exception lastException = null;

        // 重试循环
        while (retryCount < maxRetries) {
            try {
                // 构建请求JSON
                JSONObject requestJson = new JSONObject();
                requestJson.put("lines", asr5List);

                // 发送HTTP请求
                HttpResponse response = null;
                String responseBody = null;
                try {
                    String apiUrl = baseUrl + "/sync_golden3s_summary";
                    response = HttpRequest.post(apiUrl).header("Content-Type", "application/json")
                            .body(requestJson.toString()).timeout(180 * 1000).execute();
                    responseBody = response.body();
                    log.info("黄金3秒总结API响应: {}", responseBody);
                } catch (Exception e) {
                    log.error("黄金3秒总结API请求失败: {}", e.getMessage(), e);
                    throw new RuntimeException("黄金3秒总结API请求失败，原因：" + e.getMessage());
                } finally {
                    if (response != null) {
                        response.close();
                    }
                }

                // 解析响应结果
                JSONObject responseJson = new JSONObject(responseBody);
                String summary = responseJson.optString("summary");

                if (summary != null && !summary.trim().isEmpty()) {
                    log.info("黄金3秒总结API生成脚本成功");
                    return summary;
                } else {
                    // 如果没有summary字段或为空，记录日志并重试
                    log.warn("黄金3秒总结API生成脚本失败，没有返回summary字段或为空，重试第{}次", retryCount + 1);
                }
            } catch (Exception e) {
                // 记录异常并重试
                lastException = e;
                log.warn("调用黄金3秒总结API生成脚本异常，重试第{}次", retryCount + 1, e);
            }

            // 增加重试次数
            retryCount++;

            // 如果还有重试机会，等待一段时间
            if (retryCount < maxRetries) {
                try {
                    Thread.sleep(retryInterval);
                    // 每次重试后增加等待时间，避免短时间内频繁请求
                    retryInterval *= 2;
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new Exception("重试等待被中断", ie);
                }
            }
        }

        // 如果所有重试都失败，记录错误并返回失败消息
        if (lastException != null) {
            log.error("调用黄金3秒总结API生成脚本失败，已重试{}次", maxRetries, lastException);
        } else {
            log.error("调用黄金3秒总结API生成脚本失败，已重试{}次，没有返回summary字段", maxRetries);
        }

        return "黄金3秒总结API生成台词失败";
    }

    /**
     * 保存计算结果
     *
     * @param dateStr        日期字符串
     * @param industry       行业
     * @param tag            标签
     * @param videoEeIds     视频esId列表
     * @param clusterGroups  聚类组别映射
     * @param groupScriptMap 组别脚本映射
     * @param esIdToAsr5Map  视频esId到ASR 5秒结果的映射
     */
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    private void saveCalculationResults(String dateStr, String industry, String tag, List<String> videoEeIds,
            Map<String, List<String>> clusterGroups, Map<String, String> groupScriptMap,
            Map<String, String> esIdToAsr5Map) {
        // 查询cms_qianchuan_material_video表中的数据
        List<QianchuanMaterialVideo> materialVideos = qianchuanMaterialVideoMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<QianchuanMaterialVideo>()
                        .in(QianchuanMaterialVideo::getVideoId, videoEeIds));

        if (materialVideos.isEmpty()) {
            log.warn("没有找到匹配的千川素材视频数据，日期：{}，行业：{}，标签：{}", dateStr, industry, tag);
            return;
        }

        // 构建视频ID到千川素材视频的映射
        Map<String, QianchuanMaterialVideo> videoIdToMaterialMap = materialVideos.stream()
                .collect(Collectors.toMap(QianchuanMaterialVideo::getVideoId, v -> v));

        // 遍历组别脚本映射，保存计算结果
        for (Map.Entry<String, String> entry : groupScriptMap.entrySet()) {
            String groupId = entry.getKey();
            String script = entry.getValue();

            // 获取该组的esId列表
            List<String> esIdList = clusterGroups.get(groupId);
            if (esIdList == null || esIdList.isEmpty()) {
                continue;
            }

            // 初始化变量
            boolean needScriptFromAsr5 = "黄金3秒总结API生成台词失败".equals(script);
            if (needScriptFromAsr5) {
                log.warn("组ID：{} 的脚本生成失败，将使用曝光量最大的视频的ASR5内容作为脚本", groupId);
            }

            // 用于存储曝光量最大的视频信息
            String maxExposureEsId = null;
            String coverVideoUrl = null;
            String firstCoverVideoUrl = null;
            int maxExposure = -1;

            // 用于计算平均数据
            int totalInteractCount = 0;
            int totalLikeCount = 0;
            int totalCommentCount = 0;
            int totalExposureCount = 0;
            float totalRating = 0.0f; // 创意分值总和
            int validCount = 0;
            int ratingValidCount = 0; // 有效的创意分值计数

            // 一次遍历完成所有操作
            for (String esId : esIdList) {
                QianchuanMaterialVideo materialVideo = videoIdToMaterialMap.get(esId);
                if (materialVideo != null) {
                    // 1. 计算平均数据
                    // 互动数 = 点赞数 + 评论数 + 分享数
                    int interactCount = (materialVideo.getLikes() != null ? materialVideo.getLikes() : 0)
                            + (materialVideo.getComments() != null ? materialVideo.getComments() : 0)
                            + (materialVideo.getShares() != null ? materialVideo.getShares() : 0);

                    totalInteractCount += interactCount;
                    totalLikeCount += (materialVideo.getLikes() != null ? materialVideo.getLikes() : 0);
                    totalCommentCount += (materialVideo.getComments() != null ? materialVideo.getComments() : 0);
                    totalExposureCount += (materialVideo.getExposure() != null ? materialVideo.getExposure() : 0);

                    // 累加创意分值
                    if (materialVideo.getRating() != null) {
                        totalRating += materialVideo.getRating();
                        ratingValidCount++;
                    }

                    validCount++;

                    // 2. 检查是否是曝光量最大的视频
                    if (materialVideo.getExposure() != null && materialVideo.getExposure() > maxExposure) {
                        maxExposure = materialVideo.getExposure();
                        maxExposureEsId = esId;

                        // 如果有封面，同时更新封面URL
                        if (materialVideo.getCoverImage() != null) {
                            coverVideoUrl = materialVideo.getCoverImage();
                        }
                    }

                    // 3. 记录第一个有封面的视频（作为备用）
                    if (firstCoverVideoUrl == null && materialVideo.getCoverImage() != null) {
                        firstCoverVideoUrl = materialVideo.getCoverImage();
                    }
                }
            }

            // 计算平均值
            int avgInteractCount = validCount > 0 ? totalInteractCount / validCount : 0;
            int avgLikeCount = validCount > 0 ? totalLikeCount / validCount : 0;
            int avgCommentCount = validCount > 0 ? totalCommentCount / validCount : 0;
            int avgExposureCount = validCount > 0 ? totalExposureCount / validCount : 0;

            // 计算平均创意分值，保留一位小数
            String avgRating = "0.0";
            if (ratingValidCount > 0) {
                float avgRatingValue = totalRating / ratingValidCount;
                avgRating = String.format("%.1f", avgRatingValue);
            }

            // 如果脚本生成失败，使用曝光量最大的视频的ASR5内容作为脚本
            if (needScriptFromAsr5 && maxExposureEsId != null && esIdToAsr5Map.containsKey(maxExposureEsId)) {
                script = esIdToAsr5Map.get(maxExposureEsId);
                log.info("使用曝光量最大的视频（esId：{})的ASR5内容作为脚本", maxExposureEsId);
            }

            // 如果没有找到封面，使用第一个有封面的视频
            if (coverVideoUrl == null) {
                coverVideoUrl = firstCoverVideoUrl;
            }

            // 创建黄金五秒记录
            CmsVideoFiveGold fiveGold = new CmsVideoFiveGold();
            fiveGold.setIndustry(industry);
            fiveGold.setTag(tag);
            fiveGold.setAnalysisDate(dateStr);
            fiveGold.setCoverVideoUrl(coverVideoUrl);
            fiveGold.setVideoNum(esIdList.size());
            fiveGold.setExposureCount(avgExposureCount); // 设置平均曝光量
            fiveGold.setInteractCount(avgInteractCount);
            fiveGold.setLikeCount(avgLikeCount);
            fiveGold.setCommentCount(avgCommentCount);
            fiveGold.setOriginalityNum(avgRating); // 设置平均创意分值
            fiveGold.setDialogueRoutine(script); // 台词存储脚本
            fiveGold.setStatus(3); // 完成状态

            // 保存到数据库
            try {
                // 保存黄金五秒记录
                this.save(fiveGold);
                log.info("保存黄金五秒计算结果成功，组ID：{}，日期：{}，行业：{}，标签：{}", groupId, dateStr, industry, tag);

                // 保存关联视频到中间表
                for (String esId : esIdList) {
                    CmsVideoThreeGoldRelation relation = new CmsVideoThreeGoldRelation();
                    relation.setVideoThreeGoldId(fiveGold.getId());
                    relation.setVideoId(esId);
                    videoThreeGoldRelationMapper.insert(relation);
                }
                log.info("保存关联视频成功，共 {} 条，组ID：{}，日期：{}，行业：{}，标签：{}", esIdList.size(), groupId, dateStr, industry, tag);
            } catch (Exception e) {
                log.error("保存黄金五秒计算结果失败，组ID：{}，日期：{}，行业：{}，标签：{}", groupId, dateStr, industry, tag, e);
            }
        }

        // 删除前一天的黄金五秒数据及其关联数据
        try {
            // 计算前一天的日期
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            Date currentDate = formatter.parse(dateStr);
            calendar.setTime(currentDate);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            String previousDateStr = formatter.format(calendar.getTime());

            // 查询前一天的黄金五秒数据，并按行业和标签进行筛选
            LambdaQueryWrapper<CmsVideoFiveGold> fiveGoldQueryWrapper = new LambdaQueryWrapper<>();
            fiveGoldQueryWrapper.eq(CmsVideoFiveGold::getAnalysisDate, previousDateStr)
                    .eq(CmsVideoFiveGold::getIndustry, industry).eq(CmsVideoFiveGold::getTag, tag);
            List<CmsVideoFiveGold> previousFiveGolds = this.list(fiveGoldQueryWrapper);

            if (!previousFiveGolds.isEmpty()) {
                // 提取前一天黄金五秒的ID列表
                List<Integer> fiveGoldIds = previousFiveGolds.stream().map(CmsVideoFiveGold::getId)
                        .collect(Collectors.toList());

                // 软删除关联的CmsVideoThreeGoldRelation数据
                LambdaUpdateWrapper<CmsVideoThreeGoldRelation> relationUpdateWrapper = new LambdaUpdateWrapper<>();
                relationUpdateWrapper.in(CmsVideoThreeGoldRelation::getVideoThreeGoldId, fiveGoldIds)
                        .set(CmsVideoThreeGoldRelation::getIsDeleted, 1);
                int relationDeleteCount = videoThreeGoldRelationMapper.update(null, relationUpdateWrapper);

                // 软删除前一天的CmsVideoFiveGold数据，并按行业和标签进行筛选
                LambdaUpdateWrapper<CmsVideoFiveGold> fiveGoldUpdateWrapper = new LambdaUpdateWrapper<>();
                fiveGoldUpdateWrapper.eq(CmsVideoFiveGold::getAnalysisDate, previousDateStr)
                        .eq(CmsVideoFiveGold::getIndustry, industry).eq(CmsVideoFiveGold::getTag, tag)
                        .set(CmsVideoFiveGold::getIsDeleted, 1);
                int fiveGoldDeleteCount = this.baseMapper.update(null, fiveGoldUpdateWrapper);

                log.info("软删除前一天({})的黄金五秒数据成功，共删除{}条黄金五秒记录和{}条关联记录", previousDateStr, fiveGoldDeleteCount,
                        relationDeleteCount);
            } else {
                log.info("前一天({})没有黄金五秒数据，无需删除", previousDateStr);
            }
        } catch (Exception e) {
            log.error("删除前一天的黄金五秒数据失败，日期：{}，行业：{}，标签：{}", dateStr, industry, tag, e);
        }
    }

    @Override
    public void manualTriggerTask(String dateStr, String industry, String tag) {
        if (dateStr == null || dateStr.isEmpty()) {
            dateStr = DateUtil.getYYYYMMDD(new Date());
        }

        if (industry != null && !industry.isEmpty() && tag != null && !tag.isEmpty()) {

            executeGoldFiveSecondCalculation(dateStr, industry, tag);
        } else {
            // 否则执行全部行业和标签的黄金五秒计算任务
            processGoldFiveSecondTask(dateStr);
        }
    }
}
