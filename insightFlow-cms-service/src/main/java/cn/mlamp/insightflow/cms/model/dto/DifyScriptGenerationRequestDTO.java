package cn.mlamp.insightflow.cms.model.dto;

import cn.mlamp.insightflow.cms.model.query.ScriptGenerationRequest;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import java.util.Map;

/**
 * Dify脚本生成请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
public class DifyScriptGenerationRequestDTO {

    private String brand;
    private String product;
    private String sellingPoint;
    private String userStory;
    private String purchaseWording;
    private String picture;
    private String wording;

    /**
     * 构建Dify请求参数
     *
     * @param request 脚本生成请求
     * @return Dify请求参数
     */
    public static Map<String, Object> buildDifyParams(ScriptGenerationRequest request) {
        var dto = new DifyScriptGenerationRequestDTO();
        
        // 设置所有参数
        dto.setBrand(request.getBrand());
        dto.setProduct(request.getProduct());
        dto.setSellingPoint(request.getSellingPoint());
        dto.setUserStory(request.getUserStory());
        dto.setPurchaseWording(request.getPurchaseWording());
        dto.setPicture(request.getPicture());
        dto.setWording(request.getWording());

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return objectMapper.convertValue(dto, Map.class);
    }
}
