package cn.mlamp.insightflow.cms.service.webflux;


import cn.mlamp.insightflow.cms.config.properties.DifyRequestProperties;
import cn.mlamp.insightflow.cms.model.dto.DifyAiImitateRequestDTO;
import cn.mlamp.insightflow.cms.model.dto.DifyScriptGenerationRequestDTO;
import cn.mlamp.insightflow.cms.model.dto.DifyStoryboardModifyRequestDTO;
import cn.mlamp.insightflow.cms.model.dto.DifyStoryboardRecRequestDTO;
import cn.mlamp.insightflow.cms.model.query.ScriptGenerationRequest;
import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import cn.mlamp.insightflow.cms.util.dify.DifyUtil;
import cn.mlamp.insightflow.cms.util.dify.model.DifyRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DifyRequestService {
    private final DifyRequestProperties difyRequestProperties;

    private final DifyUtil difyUtil;

    public DifyRequestService(DifyRequestProperties difyRequestProperties) {
        this.difyRequestProperties = difyRequestProperties;
        this.difyUtil = new DifyUtil();
    }

    public String productInfoSummary(String productInfoStr) throws Exception {
        Map<String, Object> input = Map.of("json", productInfoStr);
        var difyRequest = DifyRequest.builder()
                .appKey(difyRequestProperties.getProductSummaryKey())
                .baseUrl(difyRequestProperties.getBaseUrl())
                .inputsParams(input)
                .observationId(UUID.randomUUID().toString().replace("-", ""))
                .build();
        log.info("产品信息摘要中");
        var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);
        if (!difyResult.containsKey("text")) {
            throw new Exception("调用dify的产品信息摘要失败");
        }
        return difyResult.get("text").toString();
    }


    public String productFileInfoSummary(MultipartFile file) throws Exception {
        Map<String, Object> input = new HashMap<>();
        input.put("caption",file.getOriginalFilename());
        input.put("content",readAllText(file));
        var difyRequest = DifyRequest.builder()
                .appKey(difyRequestProperties.getUploadProductFileKey())
                .baseUrl(difyRequestProperties.getBaseUrl())
                .inputsParams(input)
                .observationId(UUID.randomUUID().toString().replace("-", ""))
                .build();
        log.info("通过文档分析商品信息中");
        var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);
        if (!difyResult.containsKey("result")) {
            throw new Exception("调用dify的产品信息摘要失败");
        }
        return difyResult.toString();
    }
    /*
        读取文件文本内容
     */
    public String readAllText(MultipartFile file) throws IOException {

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            return reader.lines().collect(Collectors.joining("\n"));
        }
    }


    public String storyboardRecommend(VideoScriptGenRequest request, String storyBoardJson) throws Exception {
        var difyRequest = DifyRequest.builder()
                .appKey(difyRequestProperties.getStoryboardRecommendKey())
                .baseUrl(difyRequestProperties.getBaseUrl())
                .inputsParams(DifyStoryboardRecRequestDTO.buildDifyParams(request, storyBoardJson))
                .observationId(UUID.randomUUID().toString().replace("-", ""))
                .build();
        log.info("分镜推荐中");
        var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);
        if (!difyResult.containsKey("text")) {
            throw new Exception("调用dify的分镜推荐失败");
        }
        String resultStr = difyResult.get("text").toString();
        // dify输出不稳定，只获取[]中的内容（包含[]）
        Pattern pattern = Pattern.compile("\\[(.*?)\\]");
        Matcher matcher = pattern.matcher(resultStr);
        return matcher.find() ? matcher.group() : null;
    }

    public String storyboardModify(VideoScriptGenRequest request, Map<String, String> storyboard,
                                   String key, String prompt) throws Exception {
        var difyRequest = DifyRequest.builder()
                .appKey(difyRequestProperties.getStoryboardModifyKey())
                .baseUrl(difyRequestProperties.getBaseUrl())
                .inputsParams(DifyStoryboardModifyRequestDTO.buildDifyParams(request, storyboard, key, prompt))
                .observationId(UUID.randomUUID().toString().replace("-", ""))
                .build();
        log.info("AI魔法棒调用中");
        var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);
        if (!difyResult.containsKey("text")) {
            throw new Exception("调用dify的分镜修改失败");
        }
        return difyResult.get("text").toString();
    }

    public String aiImitate(VideoScriptGenRequest request, String storyBoardJson, String observationId) throws Exception {
        var difyRequest = DifyRequest.builder()
                .appKey(difyRequestProperties.getAiWriteKey())
                .baseUrl(difyRequestProperties.getBaseUrl())
                .inputsParams(DifyAiImitateRequestDTO.buildDifyParams(request, storyBoardJson))
                .observationId(observationId)
                .build();
        log.info("AI仿写生成中");
        var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);
        if (!difyResult.containsKey("text")) {
            throw new Exception("调用dify的AI仿写失败");
        }
        return difyResult.get("text").toString();
    }

    public String imagePromptGen(String query) throws RuntimeException {
        var difyRequest = DifyRequest.builder()
                .appKey(difyRequestProperties.getImagePromptTranslateKey())
                .baseUrl(difyRequestProperties.getBaseUrl())
                .query(query)
                .build();
        var difyResult = difyUtil.blockingChatRequest(difyRequest, UUID.randomUUID().toString());
        if (difyResult == null) {
            throw new RuntimeException("图片提示词翻译失败");
        }
        return difyResult;
    }

    /**
     * 人物画像生成
     *
     * @param posts        ES拉取的社交媒体数据
     * @param brand        品牌
     * @param product      产品
     * @param sellingPoint 卖点
     * @return 生成的人物画像
     * @throws Exception 调用失败异常
     */
    public String personaGeneration(String posts, String brand, String product, String sellingPoint) throws Exception {
        Map<String, Object> inputParams = Map.of("posts", posts, "brand", brand, "product", product, "sellingPoint",
                sellingPoint);

        var difyRequest = DifyRequest.builder().appKey("app-DKQaRL2ToO7bx5dw1WSZHtOV")
                .baseUrl(difyRequestProperties.getBaseUrl()).inputsParams(inputParams)
                .observationId(UUID.randomUUID().toString().replace("-", "")).build();

        log.info("调用Dify人物画像生成工作流");
        var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);

        if (!difyResult.containsKey("text")) {
            throw new Exception("调用Dify人物画像生成失败");
        }

        return difyResult.get("text").toString();
    }

    /**
     * 活动逼单话术生成
     *
     * @param activities 活动列表
     * @return 生成的逼单话术
     * @throws Exception 调用失败异常
     */
    public String activityScriptGeneration(List<String> activities) throws Exception {
        // 拼接活动参数，格式：活动1:xxx，活动2:xxx
        StringBuilder activitiesStr = new StringBuilder();
        for (int i = 0; i < activities.size(); i++) {
            if (i > 0) {
                activitiesStr.append("，");
            }
            activitiesStr.append("活动").append(i + 1).append(":").append(activities.get(i));
        }

        Map<String, Object> inputParams = Map.of("activities", activitiesStr.toString());

        var difyRequest = DifyRequest.builder().appKey(difyRequestProperties.getActivityScriptGenerationKey())
                .baseUrl(difyRequestProperties.getBaseUrl()).inputsParams(inputParams)
                .observationId(UUID.randomUUID().toString().replace("-", "")).build();

        log.info("调用Dify活动逼单话术生成工作流");
        var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);

        if (!difyResult.containsKey("text")) {
            throw new Exception("调用Dify活动逼单话术生成失败");
        }

        return difyResult.get("text").toString();
    }

    /**
     * 脚本生成
     *
     * @param request 脚本生成请求
     * @return 生成的脚本
     * @throws Exception 调用失败异常
     */
    public String scriptGeneration(ScriptGenerationRequest request) throws Exception {
        var difyRequest = DifyRequest.builder().appKey("app-cZyLBQVtTkxe2XCJSg40OjMD")
                .baseUrl(difyRequestProperties.getBaseUrl())
                .inputsParams(DifyScriptGenerationRequestDTO.buildDifyParams(request))
                .observationId(UUID.randomUUID().toString().replace("-", "")).build();

        log.info("脚本生成中");
        var difyResult = difyUtil.executeAndMergeStreaming(difyRequest);
        if (!difyResult.containsKey("text")) {
            throw new Exception("调用dify的脚本生成失败");
        }
        return difyResult.get("text").toString();
    }

}
