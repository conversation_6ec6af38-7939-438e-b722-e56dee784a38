package cn.mlamp.insightflow.cms.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
import cn.mlamp.insightflow.cms.config.properties.ObjectStorageFlowProperties;
import cn.mlamp.insightflow.cms.constant.FileConstant;
import cn.mlamp.insightflow.cms.entity.*;
import cn.mlamp.insightflow.cms.enums.*;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.CmsTaskInfoMapper;
import cn.mlamp.insightflow.cms.model.dto.DifyScriptGenResponseDTO;
import cn.mlamp.insightflow.cms.model.query.ScriptGenerationRequest;
import cn.mlamp.insightflow.cms.model.query.TaskDetailImageGenRequest;
import cn.mlamp.insightflow.cms.model.query.TaskDetailMagicRequest;
import cn.mlamp.insightflow.cms.model.query.VideoScriptGenRequest;
import cn.mlamp.insightflow.cms.model.vo.*;
import cn.mlamp.insightflow.cms.model.vo.dam.DamAssetVO;
import cn.mlamp.insightflow.cms.service.*;
import cn.mlamp.insightflow.cms.service.dam.IDamAssetService;
import cn.mlamp.insightflow.cms.service.webflux.DifyRequestService;
import cn.mlamp.insightflow.cms.service.webflux.WebClientService;
import cn.mlamp.insightflow.cms.util.FilePathBuilder;
import cn.mlamp.insightflow.cms.util.ObservationIdUtil;
import cn.mlamp.insightflow.cms.util.PageUtils;
import cn.mlamp.insightflow.cms.util.PathUtil;
import cn.mlamp.insightflow.cms.util.excel.StoryboardExport;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
@AllArgsConstructor
public class CmsTaskInfoServiceImpl extends ServiceImpl<CmsTaskInfoMapper, CmsTaskInfo>
        implements ICmsTaskInfoService {

    private final ICmsProductRecordService productRecordService;
    private final DifyRequestService difyRequestService;
    private final ICmsTaskDetailService taskDetailService;
    private final WebClientService webClientService;
    private final IVideoResultService videoResultService;
    private final CmsPullTaskDedupedDataService pullTaskDedupedDataService;
    private final QianchuanMaterialVideoService qianchuanMaterialVideoService;
    private final IVideoInfoService videoInfoService;
    private final ICmsDocumentInfoService documentInfoService;
    private final TokenUseDetailService tokenUseDetailService;
    private final ObjectStorageFlowProperties objectStorageFlowProperties;
    private final IDamAssetService damAssetService;
    private final ICmsStoryboardEmbeddingService storyboardEmbeddingService;
    private final TenantTokenService tenantTokenService;

    @Resource(name = "cmsS3FlowService")
    private IS3FlowService cmsS3FlowService;

    public void genSceneImage(Integer taskId, List<DifyScriptGenResponseDTO.Scene> scenes,
                              String observationId, Integer userId, Integer tenantId) {
        var task = this.getById(taskId);
        if (task == null) {
            log.error("任务不存在，taskId={}", taskId);
            throw new RuntimeException("任务不存在");
        }
        Integer[] status = new Integer[scenes.size()]; // 存储每个分镜的生成状态，成功后填充result_detail_id
        Arrays.fill(status, -1); // 初始化为-1
        try {
            //  根据镜头描述字段生成图片
            updateVideoTaskStatus(taskId, VideoTaskStatusEnum.IMAGE_CREATING, null);
            if (CollectionUtil.isEmpty(scenes)) { // 解析分镜，如果解析失败，抛出异常
                throw new RuntimeException("视频脚本描述解析失败");
            }
            // 新建数组，初始化数组值全-1标记未生成
            for (int r = 0; r < 6; r++) { // 重试5次
                genSceneImageRetry(taskId, scenes, status);
                if (Arrays.stream(status).allMatch(i -> i != -1)) { // 所有分镜都生成成功
                    log.info("所有分镜图片生成成功, 重试次数={}", r); // 重试次数
                    break;
                }
                log.info("部分图片生成失败，进行第{}次重试", r + 1);
            }
        } catch (Exception e) { // 生成失败
            log.error("视频分镜生出错", e);
            updateVideoTaskStatus(taskId, VideoTaskStatusEnum.IMAGE_CREATE_FAILED, e.getMessage());
            return; // 失败就退出
        } finally { // 无论成功失败，都存储result_detail_id（失败的id为-1）
            List<Integer> resultId = new ArrayList<>(Arrays.asList(status));
            this.update(new LambdaUpdateWrapper<CmsTaskInfo>()
                    .set(CmsTaskInfo::getResultFileIds, JSONUtil.toJsonStr(resultId))
                    .eq(CmsTaskInfo::getId, taskId));
        }
        log.info("视频分镜生成结束,taskId={}", taskId);
        // 生成成功, 记录消耗点数, 更新任务状态
        if (observationId != null) {
            String filename = task.getName() != null ? task.getName() : task.getId().toString();
            tokenUseDetailService.countTokenUse(observationId, taskId, TokenTaskTypeEnum.AI_IMITATE_TASK.getTaskType(),
                    filename + "-脚本生成", tenantId, userId.toString(), null);
            // 统计不等于-1的（生成成功的图片）数量
            int imageTokens = (int) Arrays.stream(status).filter(id -> id != -1).count() * 1000;
            tokenUseDetailService.saveTokenDetail(imageTokens, observationId, taskId, TokenTaskTypeEnum.AI_IMITATE_TASK.getTaskType(),
                    filename + "-图片生成", tenantId, userId);
        }
        updateVideoTaskStatus(taskId, VideoTaskStatusEnum.COMPLETED, null);

        // 异步调用存储镜头描述embedding
        storyboardEmbeddingService.asyncEmbedding(taskId, scenes, status, tenantId, userId);
    }

    private void genSceneImageRetry(Integer taskId, List<DifyScriptGenResponseDTO.Scene> scenes, Integer[] status) throws RuntimeException {
        int idx = 0;
        for (var scene : scenes) { // 每个分镜调用算法生成，如果其中某张图片失败，继续生成下一张
            if (status[idx] != -1) { // 该分镜已经生成过, 检查下一张
                idx++;
                continue;
            }
            log.info("生成第{}个分镜图片中...", idx + 1);
            try {
                // 调用dify算法生成prompt, 然后调用算法生成图片
                if (StrUtil.isEmpty(scene.getSceneDescription())) {
                    log.error("镜头描述为空，taskId={},idx={}", taskId, idx);
                    throw new RuntimeException("镜头描述为空");
                }
                // dify生成内容里加入了提示词， 先检测是否包含
                var prompt = scene.getImagePrompt();
                if (StrUtil.isEmpty(prompt)) {
                    log.info("prompt为空，taskId={},idx={}, 自动开始生成", taskId, idx);
                    prompt = difyRequestService.imagePromptGen(scene.getSceneDescription());
                }
                String imageGenResult = webClientService.genImageBySD(prompt).block();
                // 读取String为Json对象
                var imageDto = JSONUtil.toBean(imageGenResult, WebClientService.ImageGenResponse.class);
                if (StrUtil.isEmpty(imageGenResult) || imageDto.getCode() == null || !imageDto.getCode().equals("200")) {
                    log.error("视频分镜生成失败，taskId={},idx={}", taskId, idx);
                    log.error("imageGenResult={}", imageGenResult);
                    throw new RuntimeException("视频分镜生成失败");
                }
                // 生成成功，存储算法图片结果结果（存入oss）
                String objId = uploadImageToOss(imageDto.getImageBase64()); // 上传图片到oss
                var taskResultDetail = taskDetailService.saveTaskDetail(taskId, TaskDetailTypeEnum.OUTPUT.getCode(),
                        TaskDetailDataTypeEnum.ALGORITHM.getCode(), objId);
                status[idx] = taskResultDetail.getId(); // 保存result_detail_id
            } catch (RuntimeException re) {
                log.error("调用生成分镜图片失败，taskId={},idx={}", taskId, idx, re);
            } finally {
                idx++; // 下一个分镜idx
            }
        }
    }

    private String uploadImageToOss(String base64) throws RuntimeException {
        String localFilePath = System.getProperty("user.dir") + "/" + UUID.fastUUID() + ".png";
        try {
            // 1. 解码Base64字符串为字节数组
            byte[] imageBytes = Base64.getDecoder().decode(base64);

            // 2. 将字节数组写入图片文件
            try (FileOutputStream fos = new FileOutputStream(localFilePath)) {
                fos.write(imageBytes);
            }
            String ossPath = FilePathBuilder.generateImageOssPath(FileConstant.OSS_PATH_AI_IMAGE_PREFIX,
                    PathUtil.pathToSuffix(localFilePath));
            try {
                cmsS3FlowService.upload(ossPath, new File(localFilePath));
            } catch (Exception e) {
                log.error("上传文件到oss失败", e);
                throw new RuntimeException("上传文件到oss失败", e);
            }
            return ossPath;
        } catch (IOException e) {
            log.error("保存图片失败: " + e.getMessage());
            throw new RuntimeException("保存图片失败");
        } catch (IllegalArgumentException e) {
            log.error("Base64字符串无效: " + e.getMessage());
            throw new RuntimeException("Base64字符串无效");
        } finally {
            // 删除文件
            FileUtil.del(localFilePath);
        }
    }

    @Override
    public Integer videoTaskAiImitate(VideoScriptGenRequest request) {
        // 上传之前判断token点数
        if (tenantTokenService.checkBalance(request.getTenantId()) <= 0) {
            throw new RuntimeException("租户点数不足");
        }
        var task = new CmsTaskInfo();
        task.setVideoId(request.getSourceId());
        task.setTaskType(TaskTypeEnum.AI_WRITE.getCode());
        // 脚本标题 = 必填参数+时间戳 如：欧莱雅_护肤精华_60S_6镜头数_20250327
        var input = request.getContent();
        // 保存商品信息
        productRecordService.saveByScriptInput(input, request.getUserId(), request.getTenantId());
        // 保存任务
        String name = input.getBrand() + "_" + input.getProduct() + "_"
                // 新版本这两个参数非必填
//                + input.getDuration() + "S_" + input.getLensNum() + "镜头数_"
                + input.getSellingPoint()
                + DateUtil.format(new Date(), "yyyyMMddHHmmss");
        task.setName(name);
        task.setTaskStatus(VideoTaskStatusEnum.QUEUING.getCode());
        task.setTaskArg(JSONUtil.toJsonStr(request));
        task.setUserId(request.getUserId());
        task.setTenantId(request.getTenantId());
        this.save(task);
        return task.getId();
    }

    @Async("commonTaskExecutor")
    @Override
    // 异步AI仿写
    public void videoTaskAiImitateAsync(VideoScriptGenRequest request, Integer taskId) {
        List<DifyScriptGenResponseDTO.Scene> scenes;
        String difyResult;
        var observationId = ObservationIdUtil.getObservationId(TokenTaskTypeEnum.AI_IMITATE_TASK);
        try {
            // 调用AI仿写dify，成功后存入结果数据库，改变状态
            updateVideoTaskStatus(taskId, VideoTaskStatusEnum.SCRIPT_CREATING, null);
            // 读取参考分镜信息，转json
            String storyBoardJsonStr = null;
            if (!request.getStoryboardIds().isEmpty()) {
                List<JSONObject> storyBoardData = videoResultService.getVideoResult(
                                request.getStoryboardIds().stream().map(Integer::valueOf).collect(Collectors.toList()))
                        .stream().map(JSONUtil::parseObj).toList();
                // 将json字符串转成json数组对象
                storyBoardJsonStr = JSONUtil.toJsonStr(storyBoardData);
            }
            difyResult = difyRequestService.aiImitate(request, storyBoardJsonStr, observationId);
            if (StrUtil.isEmpty(difyResult)) {
                log.error("调用dify仿写为空，taskId={}", taskId);
                throw new RuntimeException("AI仿写失败");
            }
            // 存储dify结果
            taskDetailService.saveTaskDetail(taskId,
                    TaskDetailTypeEnum.OUTPUT.getCode(),
                    TaskDetailDataTypeEnum.DIFY.getCode(),
                    difyResult);
            scenes = DifyScriptGenResponseDTO.parseScriptByText(difyResult);
        } catch (Exception e) { // 生成失败
            log.error("AI仿写失败", e);
            updateVideoTaskStatus(taskId, VideoTaskStatusEnum.SCRIPT_CREATE_FAILED, e.getMessage());
            return;
        }
        // 生成分镜图片
        genSceneImage(taskId, scenes, observationId, request.getUserId(), request.getTenantId());
    }

    @Async("commonTaskExecutor")
    @Override
    public void scriptGenerationAsync(ScriptGenerationRequest request, Integer taskId) {
        List<DifyScriptGenResponseDTO.Scene> scenes;
        String difyResult;
        var observationId = ObservationIdUtil.getObservationId(TokenTaskTypeEnum.AI_IMITATE_TASK);

        try {
            // 更新任务状态为脚本生成中
            updateVideoTaskStatus(taskId, VideoTaskStatusEnum.SCRIPT_CREATING, null);

            // 调用Dify生成脚本
            difyResult = difyRequestService.scriptGeneration(request);
            if (StrUtil.isEmpty(difyResult)) {
                log.error("调用dify脚本生成为空，taskId={}", taskId);
                throw new RuntimeException("脚本生成失败");
            }

            // 存储dify结果
            taskDetailService.saveTaskDetail(taskId, TaskDetailTypeEnum.OUTPUT.getCode(),
                    TaskDetailDataTypeEnum.DIFY.getCode(), difyResult);

            // 解析脚本内容
            scenes = DifyScriptGenResponseDTO.parseScriptByText(difyResult);

        } catch (Exception e) {
            log.error("脚本生成失败", e);
            updateVideoTaskStatus(taskId, VideoTaskStatusEnum.SCRIPT_CREATE_FAILED, e.getMessage());
            return;
        }

        // 生成分镜图片
        genSceneImage(taskId, scenes, observationId, request.getUserId(), request.getTenantId());
    }

    @Override
    public Page<VideoTaskListInfoVO> videoTaskPage(Integer current, Integer pageSize, Integer type, Integer userId, Integer tenantId) {
        // 可以写成一个sql查询
        // type: 0:脚本生成任务（包含套路复用和AI仿写） 1:帖子打标任务
        switch (type) {
            case 0 -> {
                var page = this.page(new Page<>(current, pageSize),
                        new LambdaQueryWrapper<CmsTaskInfo>()
//                                .eq(CmsTaskInfo::getUserId, userId)
                                .eq(CmsTaskInfo::getTaskType, TaskTypeEnum.AI_WRITE.getCode())
                                .eq(CmsTaskInfo::getTenantId, tenantId)
                                .orderByDesc(CmsTaskInfo::getCreateTime)
                );
                var taskIds = page.getRecords().stream().map(CmsTaskInfo::getId).toList();
                Map<Integer, Integer> tokenUseDetailMap = taskIds.isEmpty() ? Map.of()
                        : tokenUseDetailService.getTokenDetailByTypeAndTaskIdIn(2, taskIds)
                        .stream()
                        .collect(Collectors.toMap(TokenUseDetail::getTaskId, TokenUseDetail::getTokens, Integer::sum));
                var result = PageUtils.convertVOPage(page, VideoTaskListInfoVO::mapperByCmsTaskInfo);
                // 装载原视频标题，文案（台词）和分镜信息（id和图片）
                for (var taskInfo : result.getRecords()) {
                    if (tokenUseDetailMap.containsKey(taskInfo.getId())) {
                        taskInfo.setPoints(tokenUseDetailMap.get(taskInfo.getId()));
                    }
                    String esId = taskInfo.getEsId(); // 目前存esId
                    var video = pullTaskDedupedDataService.findFirstByEsId(esId);
                    if (video != null) {
                        taskInfo.setSourceTitle(video.getTextContent());
                    }
                    // 已完成的任务需要加载台词和分镜图片
                    if (VideoTaskStatusEnum.COMPLETED.getCode().equals(taskInfo.getStatus())) {
                        var userStoryboard = this.getUserStoryboard(taskInfo.getId(), null, true);
                        taskInfo.setText(userStoryboard.stream().map(VideoStoryBoardVO::getDialogue)
                                .filter(StrUtil::isNotEmpty).collect(Collectors.joining("\n")));
                        List<VideoStoryBoardVO> newStoryboards = new ArrayList<>();
                        for (var sb : userStoryboard) {
                            var newSb = new VideoStoryBoardVO();
                            // 装载图片
                            if (sb.getObjOssId() != null) {
                                newSb.setId(sb.getId());
                                newSb.setObjOssId(sb.getObjOssId());
                                newSb.setUrl(sb.getUrl());
                            }
                            newStoryboards.add(newSb);
                        }
                        taskInfo.setStoryBoard(newStoryboards);
                    }
                }
                return result;
            }
            case 1 -> { //  查询打标任务
                var result = videoInfoService.pageUserVideoTask(current, pageSize, tenantId);
                for (var videoInfo : result.getRecords()) {
                    // 设置文案（帖子打标没有台词，使用镜头描述）和分镜图片(只有成功才展示，避免查询速度太慢)
                    var userVideoResults = videoInfo.getStatus() == VideoInfoStatusEnum.SUCCESS.getCode()
                            ? videoResultService.getSceneSplitResultByVideoId(videoInfo.getId()) : null;
                    if (CollectionUtil.isNotEmpty(userVideoResults)) { // 用户结果
                        List<String> dialogText = new ArrayList<>();
                        List<VideoStoryBoardVO> storyBoards = new ArrayList<>();
                        for (var videoResult : userVideoResults) {
                            if (StrUtil.isNotBlank(videoResult.getData())) {
                                JSONObject resultDataObj = JSONUtil.parseObj(videoResult.getData());
                                if (resultDataObj.containsKey("镜头描述")) {
                                    var dialog = resultDataObj.get("镜头描述").toString();
                                    if (StrUtil.isNotEmpty(dialog) && !dialog.equals("无")) {
                                        dialogText.add(resultDataObj.get("镜头描述").toString());
                                    }
                                }
                                if (resultDataObj.containsKey("sceneDecodingSegmentPicOssId")) {
                                    var ossId = resultDataObj.get("sceneDecodingSegmentPicOssId").toString();
                                    if (StrUtil.isNotEmpty(ossId)) {
                                        var storyboard = new VideoStoryBoardVO();
                                        storyboard.setId(videoResult.getId());
                                        storyboard.setObjOssId(ossId);
                                        storyboard.setUrl(this.getPicDownloadSignatureUrl(ossId));
                                        storyBoards.add(storyboard);
                                    }
                                }
                            }
                        }
                        videoInfo.setText(String.join("\n", dialogText));
                        videoInfo.setStoryBoard(storyBoards);
                    }
                }
                return result;
            }
        }
        return null;
    }

    @Override
    public List<DocTaskUploadVO> videoTaskStatus(Integer userId, Integer tenantId, List<Integer> taskIds) {
        return this.list(new LambdaQueryWrapper<CmsTaskInfo>()
                        .eq(CmsTaskInfo::getUserId, userId)
                        .eq(CmsTaskInfo::getTenantId, tenantId)
                        .in(CmsTaskInfo::getId, taskIds))
                .stream().map(DocTaskUploadVO::mapperByTaskInfo)
                .collect(Collectors.toList());
    }

    @Override
    public VideoTaskScriptDetailVO videoTaskDetail(Integer taskId, Integer userId, Integer tenantId) {
        // 校验权限和任务状态
        var task = this.getById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        if (!VideoTaskStatusEnum.COMPLETED.getCode().equals(task.getTaskStatus())) {
            throw new RuntimeException("任务未完成");
        }
        var vo = new VideoTaskScriptDetailVO();
        vo.setTaskId(taskId);
        // 原视频标题
        String videoId = task.getVideoId();
        String esId;
        AnalysisVideoTypeEnum analysisVideoType;
        // 判断videoId是否为纯数字(兼容esId)，需要返回videoId
        if (videoId.matches("\\d+")) {
            var videoInfo = videoInfoService.getById(Integer.valueOf(videoId));
            analysisVideoType = AnalysisVideoTypeEnum.getByCode(videoInfo.getType());
            if (videoInfo == null) {
                throw new RuntimeException("分镜任务不存在");
            }
            esId = videoInfo.getEsId();
            // 详情展示内容变更(新增入参)
            vo.setSourceId(task.getVideoId());
        } else {
            esId = videoId;
            var videoInfo = videoInfoService.findOneByEsId(esId);
            if (videoInfo == null) {
                throw new RuntimeException("分镜任务不存在");
            }
            analysisVideoType = AnalysisVideoTypeEnum.getByCode(videoInfo.getType());
            vo.setSourceId(videoInfo.getId().toString());
        }
        if (analysisVideoType != null) {
            switch (analysisVideoType) {
                case VIDEO_ANALYSIS, UPLOAD_VIDEO -> { //用户上传读取cms_pull_task_deduped_datas表获取标题
                    var video = pullTaskDedupedDataService.findFirstByEsId(esId);
                    if (video != null) {
                        vo.setTitle(video.getTextContent());
                    }
                }
                case QIANCHUAN_VIDEO -> { //千川上传读取cms_qianchuan_material_video获取标题
                    var video = qianchuanMaterialVideoService.getOne(
                            new LambdaQueryWrapper<QianchuanMaterialVideo>()
                                    .eq(QianchuanMaterialVideo::getVideoId, esId)
                    );
                    if (video != null) {
                        vo.setTitle(video.getTitle());
                    }
                }
            }
        }
        // 入参信息
        VideoScriptGenRequest request = JSONUtil.toBean(task.getTaskArg(), VideoScriptGenRequest.class);
        if (request != null) {
            if (request.getContent() != null) { // 读取入参
                vo.setContentInput(request.getContent());
            }
            // 参考分镜
            if (!request.getStoryboardIds().isEmpty()) {
                List<JSONObject> storyBoardData = videoResultService.getVideoResult(
                                request.getStoryboardIds().stream().map(Integer::valueOf).collect(Collectors.toList()))
                        .stream().map(JSONUtil::parseObj).toList();
                List<VideoTaskScriptDetailVO.ReferenceStoryBoardVO> referenceStoryboard = new ArrayList<>();
                for (int idx = 0; idx < request.getStoryboardIds().size(); idx++) {
                    var sb = new VideoTaskScriptDetailVO.ReferenceStoryBoardVO();
                    sb.setId(request.getStoryboardIds().get(idx));
                    var data = storyBoardData.get(idx);
                    if (data.containsKey("镜头描述")) {
                        sb.setName(data.get("镜头描述").toString());
                    }
                    // 黄金3s判断
                    sb.setGolden(data.containsKey("highlight") && data.get("highlight").toString().equals("1"));
                    referenceStoryboard.add(sb);
                }
                vo.setReferenceStoryboard(referenceStoryboard);
            }
        }
        var userStoryboard = this.getUserStoryboard(taskId, null, true);
        vo.setStoryBoards(userStoryboard);
        return vo;
    }

    public String getPicDownloadSignatureUrl(String picOssId) {
        final URL url;
        try {
            url = cmsS3FlowService.downloadPresignedUrl(objectStorageFlowProperties.getCms().getBucketName(), picOssId, 14400, 0);
        } catch (Exception e) {
            log.error("获取其他文件 其OSS路径为 {} 的访问链接失败", picOssId, e);
            throw new BusinessException("签名失败");
        }
        return url.toString();
    }

    @Override
    public void exportVideoTask(Integer taskId, Integer storyboardId, HttpServletResponse response) throws IOException {
        final StringJoiner joiner = new StringJoiner("-", "", ".xlsx");
        joiner.add("分镜导出");
        joiner.add(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePattern.CHINESE_DATE_TIME_PATTERN)));
        final String filename = joiner.toString();

        List<VideoStoryBoardExcelVO> storyboardExport = new ArrayList<>();
        // 加入图片(新版本存储的ossId)
        var imageHandler = new StoryboardExport.CellImageWriteHandler();

        Map<String, byte[]> imageMap = new HashMap<>();

        // 查询用户保存的数据
        var userStoryboards = this.getUserStoryboard(taskId, storyboardId, false);
        int index = 1;
        for (var storyboard : userStoryboards) {
            var exportData = new VideoStoryBoardExcelVO();
            BeanUtils.copyProperties(storyboard, exportData);
            String resultId = storyboard.getId().toString();
            exportData.setSceneNumber(index);  // 分镜编号
            exportData.setDuration(storyboard.getSceneLength() + "秒"); // 分镜时长
            exportData.setReferenceImage(resultId);
            this.addImageByteIntoMap(resultId, storyboard.getObjOssId(), imageMap);
            storyboardExport.add(exportData);
            index++;
        }

        imageHandler.setBase64Map(imageMap);
        try {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Pragma", "No-cache");
            response.setHeader("Cache-Control", "No-cache");
            response.setDateHeader("Expires", 0);
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8));

            ServletOutputStream outputStream = response.getOutputStream();
            EasyExcel.write(outputStream, VideoStoryBoardExcelVO.class)
                    .registerWriteHandler(imageHandler) // 注册图片处理器
                    .inMemory(true)
                    .autoCloseStream(true)
                    .sheet("分镜脚本")
                    .doWrite(storyboardExport);
        } catch (Exception e) {
            log.error("导出分镜失败", e);
        }
    }

    private void addImageByteIntoMap(String key, String ossId, Map<String, byte[]> imageMap) {
        try {
            InputStream inputStream = cmsS3FlowService.download(objectStorageFlowProperties.getCms().getBucketName(), ossId);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }
            imageMap.put(key, byteArrayOutputStream.toByteArray());
        } catch (Exception e) {
            log.error("taskDetailId={},图片下载失败", key);
        }
    }

    @Override
    public void videoTaskCancel(Integer taskId, Integer userId, Integer tenantId) {
        this.updateVideoTaskStatus(VideoTaskStatusEnum.CANCELED, "任务取消", taskId, userId, tenantId);
        // 取消的任务不需要再次继续执行
    }

    @Override
    public VideoTaskStatusEnum videoTaskRetry(Integer taskId, Integer userId, Integer tenantId) {
        var lastStatusCode = this.getById(taskId).getTaskStatus();
        this.updateVideoTaskStatus(VideoTaskStatusEnum.QUEUING, "任务重试", taskId, userId, tenantId);
        return VideoTaskStatusEnum.getByCode(lastStatusCode);
    }


    @Async("commonTaskExecutor")
    @Override
    public void retryVideoTaskAsync(Integer taskId, VideoTaskStatusEnum taskStatusEnum) {
        // 删除之前的任务结果
        taskDetailService.deleteByTaskId(taskId);
        var task = this.getById(taskId);
        if (task != null) {
            VideoScriptGenRequest request = JSONUtil.toBean(task.getTaskArg(), VideoScriptGenRequest.class);
            videoTaskAiImitateAsync(request, taskId);
        }
    }


    @Override
    public int updateVideoTaskStatus(VideoTaskStatusEnum statusEnum, String errorMsg,
                                     Integer taskId, Integer userId, Integer tenantId) {
        return this.baseMapper.update(new LambdaUpdateWrapper<CmsTaskInfo>()
                .set(CmsTaskInfo::getTaskStatus, statusEnum.getCode())
                .set(StrUtil.isNotBlank(errorMsg), CmsTaskInfo::getErrorMessage, errorMsg)
                .eq(CmsTaskInfo::getId, taskId)
                .eq(CmsTaskInfo::getUserId, userId)
                .eq(CmsTaskInfo::getTenantId, tenantId)
        );
    }

    @Override
    public void deleteVideoTask(Integer taskId, Integer userId, Integer tenantId) {
        this.baseMapper.delete(new LambdaQueryWrapper<CmsTaskInfo>()
                        .eq(CmsTaskInfo::getId, taskId)
//                .eq(CmsTaskInfo::getUserId, userId)
                        .eq(CmsTaskInfo::getTenantId, tenantId)
        );
    }

    @Override
    public List<Integer> storyBoardRecommend(VideoScriptGenRequest request) {
        var storyboardResult = videoResultService.getSceneSplitResultByVideoId(request.getTaskId());
        if (CollectionUtil.isEmpty(storyboardResult)) {
            throw new BusinessException("分镜结果不存在");
        }
        List<JSONObject> storyboardData = storyboardResult.stream()
                .map(result -> {
                    var scene = JSONUtil.parseObj(result.getData());
                    scene.put("分镜", result.getIndex().toString());
                    return scene;
                })
                .toList();
        String storyboardStr = new JSONArray(storyboardData).toString();
        try {
            var difyResult = difyRequestService.storyboardRecommend(request, storyboardStr);
            List<Integer> recommendIndex = JSONUtil.toList(JSONUtil.parseArray(difyResult), Integer.class);
            if (CollectionUtil.isEmpty(recommendIndex)) {
                throw new BusinessException("暂无推荐");
            }
            // 获取下标和id映射
            Map<Integer, Integer> storyboardMap = storyboardResult.stream()
                    .collect(Collectors.toMap(CmsVideoResult::getIndex, CmsVideoResult::getId));
            // 获取推荐分镜id
            return recommendIndex.stream()
                    .filter(storyboardMap::containsKey)
                    .map(storyboardMap::get).toList();
        } catch (Exception e) {
            log.error("分镜推荐失败", e);
            throw new BusinessException("分镜推荐失败");
        }
    }

    @Override
    public VideoTaskScriptDetailVO updateVideoTaskDetail(VideoTaskScriptDetailVO videoTaskScriptDetailVO, Integer userId, Integer tenantId) {
        // 更新时判断之前的值
        var oldStoryBoards = this.getUserStoryboard(videoTaskScriptDetailVO.getTaskId(), null, false);
        // 异步更新镜头描述embedding(需要先判断是否变更，所以要传入旧值)
        storyboardEmbeddingService.asyncUpdate(oldStoryBoards, videoTaskScriptDetailVO.getStoryBoards());

        var userTaskDetail = taskDetailService.getUserTaskDetail(videoTaskScriptDetailVO.getTaskId());
        var storyboards = videoTaskScriptDetailVO.getStoryBoards();
        for (var sb : storyboards) { // 删除一次性字段
            sb.setUrl(null);
            sb.setDetailMap(null);
        }
        String jsonStr = JSONUtil.toJsonStr(storyboards);
        if (userTaskDetail == null) { //不存在则保存
            var userSaveDetail = new CmsTaskDetail();
            userSaveDetail.setTaskId(videoTaskScriptDetailVO.getTaskId());
            userSaveDetail.setType(TaskDetailTypeEnum.OUTPUT.getCode());
            userSaveDetail.setDataType(TaskDetailDataTypeEnum.USER_SAVE.getCode());
            userSaveDetail.setData(jsonStr);
            taskDetailService.save(userSaveDetail);
        } else { // 存在则更新
            userTaskDetail.setData(jsonStr);
            taskDetailService.updateById(userTaskDetail);
        }

        return videoTaskScriptDetailVO;
    }

    @Override
    public String videoTaskDetailMagic(TaskDetailMagicRequest request) {
        var taskId = request.getTaskId();
        // 获取任务入参
        var task = this.getById(taskId);
        if (task == null) {
            throw new BusinessException("任务不存在");
        }
        VideoScriptGenRequest taskArg = JSONUtil.toBean(task.getTaskArg(), VideoScriptGenRequest.class);
        var inputArgs = VideoStoryBoardVO.getInputArgMap();
        if (!inputArgs.containsKey(request.getContentKey())) {
            throw new BusinessException("参数不存在");
        }
        var inputKey = inputArgs.get(request.getContentKey());
        // 获取用户分镜信息(不带图片)
        var userStoryboards = this.getUserStoryboard(taskId, request.getStoryboardId(), false);
        if (CollectionUtil.isEmpty(userStoryboards)) {
            throw new BusinessException("分镜不存在");
        }
        var storyboard = userStoryboards.get(0).getDetailMap();
        try {
            return difyRequestService.storyboardModify(taskArg, storyboard, inputKey, request.getContentValue());
        } catch (Exception e) {
            log.error("调用分镜魔术棒失败", e);
            throw new BusinessException("调用分镜魔术棒失败");
        }
    }

    @Override
    public VideoStoryBoardVO videoTaskDetailImageGen(TaskDetailImageGenRequest request) {
        if (request.getImageDesc() == null) { // 图片描述为空，就调用魔术棒重新生成
            var magicRequest = new TaskDetailMagicRequest();
            magicRequest.setTaskId(request.getTaskId());
            magicRequest.setStoryboardId(request.getStoryboardId());
            magicRequest.setContentKey("sceneDescription"); // 重新获取镜头描述作为图片描述
            magicRequest.setContentValue(request.getImagePrompt());
            var imageDesc = this.videoTaskDetailMagic(magicRequest);
            request.setImageDesc(imageDesc);
        }
        // 调用翻译生成prompt
        log.info("调用翻译生成prompt");
        String imagePrompt = difyRequestService.imagePromptGen(request.getImageDesc());
        // 调用图片生成
        log.info("调用图片生成");
        String imageGenResult = webClientService.genImageBySD(imagePrompt).block();
        // 读取String为Json对象
        var imageDto = JSONUtil.toBean(imageGenResult, WebClientService.ImageGenResponse.class);
        if (StrUtil.isEmpty(imageGenResult) || imageDto.getCode() == null || !imageDto.getCode().equals("200")) {
            log.error("imageGenResult={}", imageGenResult);
            throw new RuntimeException("图片生成失败");
        }
        // 生成成功，存储算法图片结果结果（存入oss）
        String objId = uploadImageToOss(imageDto.getImageBase64()); // 上传图片到oss
        String imageUrl = this.getPicDownloadSignatureUrl(objId);
        var result = new VideoStoryBoardVO();
        result.setObjOssId(objId);
        result.setUrl(imageUrl);
        return result;
    }

    @Override
    public VideoTaskScriptDetailVO recommendVideo(Integer taskId, String aspectRatio, Integer userId, Integer tenantId) {
        var vo = this.videoTaskDetail(taskId, userId, tenantId);
        // 1. 查询当前脚本的所有分镜embedding
        Map<Integer, CmsStoryboardEmbedding> storyboardEmbeddingMap = storyboardEmbeddingService.getByTaskId(taskId)
                .stream().collect(Collectors.toMap(CmsStoryboardEmbedding::getId, Function.identity()));
        // 2. 查询完所有素材后再封装标签信息
        int recommendCount = 0;
        List<VideoStoryBoardVO> needSaveEmbedding = new ArrayList<>();
        Map<Integer, List<DamAssetVO>> recommendMap = new HashMap<>();
        Set<Integer> assertIds = new HashSet<>();
        for (var sb : vo.getStoryBoards()) {
            String content = sb.getSceneDescription(); // 镜头描述语义搜索
            Integer topK = 5; // 推荐个数
            Float threshold = 0.35f; // 相似度阈值
            // 获取镜头描述embedding
            var embedding = storyboardEmbeddingMap.get(sb.getId()) == null
                    ? null : storyboardEmbeddingMap.get(sb.getId()).getEmbedding();
            if (embedding == null) {
                needSaveEmbedding.add(sb);
            }
            // 获取推荐
            var topResult = damAssetService.topKAssets(aspectRatio, content, embedding, topK, threshold, userId, tenantId);
            // 记录素材和推荐关系
            recommendMap.put(sb.getId(), topResult);
            assertIds.addAll(topResult.stream().map(DamAssetVO::getId).collect(Collectors.toSet()));
            recommendCount += topResult.size();
        }
        log.info("taskId={},推荐个数={}", taskId, recommendCount);

        // 3. 批量获取素材标签并塞入推荐
        setAssetTagsAndRecommend(vo, recommendMap, assertIds);

        // 4. 异步保存embedding
        if (!needSaveEmbedding.isEmpty()) {
            storyboardEmbeddingService.asyncEmbedding(taskId, needSaveEmbedding, tenantId, userId);
        }
        return vo;
    }

    private void setAssetTagsAndRecommend(VideoTaskScriptDetailVO vo, Map<Integer, List<DamAssetVO>> recommendMap, Set<Integer> assertIds) {
        var assetTagsMap = damAssetService.getTagsByAssertIds(assertIds);
        for (var sb : vo.getStoryBoards()) {
            var recommend = recommendMap.get(sb.getId());
            if (recommend != null) {
                for (var asset : recommend) {
                    asset.setTags(assetTagsMap.get(asset.getId()));
                }
                sb.setRecommendVideos(VideoStoryBoardVO.mapperRecommendVideoByAssetVO(recommend));
            }
        }
    }

    /**
     * 获取用户的分镜信息（不带图片）（列表，详情, 导出都给可以用）
     */
    public List<VideoStoryBoardVO> getUserStoryboard(Integer taskId, Integer storyboardId, boolean needImage) {
        // 查询结果数据
        Map<String, List<CmsTaskDetail>> result = taskDetailService.getTaskResult(taskId).
                stream().collect(Collectors.groupingBy(CmsTaskDetail::getDataType));
        // 使用getDifyVideoScriptDTO 装填脚本内容
        List<VideoStoryBoardVO> storyBoards = new ArrayList<>();
        // 分镜信息如果存在用户修改(detailType=2)则直接读取分镜数据（图片替换或删除直接替换objOssId）
        boolean userDetail = result.containsKey(TaskDetailDataTypeEnum.USER_SAVE.getCode());
        if (userDetail) { // 用户修改过
            var storyboardStr = result.get(TaskDetailDataTypeEnum.USER_SAVE.getCode()).get(0).getData();
            storyBoards = JSONUtil.toList(JSONUtil.parseArray(storyboardStr), VideoStoryBoardVO.class);
            for (var sb : storyBoards) {
                // 装载图片
                if (sb.getObjOssId() != null && needImage) {
                    sb.setUrl(this.getPicDownloadSignatureUrl(sb.getObjOssId()));
                }
                // 装载detailMap
                sb.setDetailMap(VideoStoryBoardVO.loadDetailMap(sb));
            }
        }
        // 获取dify的结果
        if (!userDetail && result.containsKey(TaskDetailDataTypeEnum.DIFY.getCode())) {
            var scenes = DifyScriptGenResponseDTO.parseScriptByText(
                    result.get(TaskDetailDataTypeEnum.DIFY.getCode()).get(0).getData());
            Map<Integer, VideoStoryBoardVO> indexSbMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(scenes)) {
                int idx = 0;
                for (var scene : scenes) {
                    var sb = new VideoStoryBoardVO();
                    BeanUtils.copyProperties(scene, sb);
                    storyBoards.add(sb);
                    indexSbMap.put(idx, sb);
                    idx++;
                }
            }
            if (result.containsKey(TaskDetailDataTypeEnum.ALGORITHM.getCode()) && !scenes.isEmpty()) {
                var images = result.get(TaskDetailDataTypeEnum.ALGORITHM.getCode());
                if (CollectionUtil.isNotEmpty(images)) {
                    Map<Integer, CmsTaskDetail> imageMap = images.stream().collect(Collectors.toMap(CmsTaskDetail::getId, v -> v));
                    // 批量获取图片签名
                    CmsTaskInfo taskInfo = this.getById(taskId);
                    List<Integer> imageIds = JSONUtil.toList(JSONUtil.parseArray(taskInfo.getResultFileIds()), Integer.class);
                    int idx = 0;
                    for (var imageId : imageIds) {
                        if (imageId != -1) {
                            var image = imageMap.get(imageId);
                            // 展示有图的
                            if (indexSbMap.get(idx) != null) {
                                indexSbMap.get(idx).setId(imageId);
                                // data现在装载的是ossId
                                indexSbMap.get(idx).setObjOssId(image.getData());
                                // 装载签名后的url
                                if (needImage) {
                                    indexSbMap.get(idx).setUrl(this.getPicDownloadSignatureUrl(image.getData()));
                                }
                            }
                        }
                        idx++;
                    }
                }
            }
        }
        if (storyboardId != null) {
            storyBoards = storyBoards.stream().filter(sb -> sb.getId().equals(storyboardId)).toList();
        }
        return storyBoards;
    }

    @Override
    public VideoTaskScriptDetailVO videoTaskDetailReverse(Integer taskId, Integer userId, Integer tenantId) {
        // 还原直接删除用户修改的脚本
        taskDetailService.update(new LambdaUpdateWrapper<CmsTaskDetail>()
                .set(CmsTaskDetail::getIsDeleted, 1)
                .eq(CmsTaskDetail::getTaskId, taskId)
                .eq(CmsTaskDetail::getDataType, TaskDetailDataTypeEnum.USER_SAVE.getCode())
        );
        return null;
    }

    private void updateVideoTaskStatus(Integer taskId, VideoTaskStatusEnum statusEnum, String errorMsg) {
        this.baseMapper.update(new LambdaUpdateWrapper<CmsTaskInfo>()
                .set(CmsTaskInfo::getTaskStatus, statusEnum.getCode())
                .set(StrUtil.isNotBlank(errorMsg), CmsTaskInfo::getErrorMessage, errorMsg)
                .eq(CmsTaskInfo::getId, taskId)
        );
    }
}
